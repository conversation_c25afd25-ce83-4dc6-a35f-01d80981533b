/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable standalone output for Azure App Service
  output: 'standalone',
  
  // Disable static optimization for API routes
  experimental: {
    serverComponentsExternalPackages: ['mongodb']
  },
  
  // Environment variables
  env: {
    MONGODB_URI: process.env.MONGODB_URI,
    ZOHO_PAYMENT_SESSION_URL: process.env.ZOHO_PAYMENT_SESSION_URL,
    ZOHO_PAY_ACCOUNT_ID: process.env.ZOHO_PAY_ACCOUNT_ID,
    ZOHO_PAY_API_KEY: process.env.ZOHO_PAY_API_KEY,
    ZOHO_WEBHOOK_SECRET: process.env.ZOHO_WEBHOOK_SECRET,
    NEXT_PUBLIC_DOMAIN: process.env.NEXT_PUBLIC_DOMAIN,
    NEXT_PUBLIC_API_DOMAIN: process.env.NEXT_PUBLIC_API_DOMAIN,
  },
  
  // Headers for API routes
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET, POST, PUT, DELETE, OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
        ],
      },
    ]
  },
  
  // Redirects for production
  async redirects() {
    return [
      {
        source: '/payment/success',
        destination: '/payment/success',
        permanent: false,
      },
    ]
  },
}

module.exports = nextConfig
